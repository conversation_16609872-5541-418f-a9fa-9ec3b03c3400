# Linux等保基线核查工具需求文档（简化版）

## 1. 项目概述

### 1.1 项目背景
开发一款基于Wails GUI框架的Linux等保基线核查工具，通过SSH远程连接方式对目标Linux服务器进行安全基线检查。

### 1.2 项目目标
- 提供简洁的图形化界面
- 支持SSH远程执行核查命令
- 生成标准化的核查报告
- 内置等保基线检查规则

### 1.3 技术架构
- **前端技术**: HTML5 + CSS3 + JavaScript (原生)
- **后端语言**: Go 1.19+
- **GUI框架**: Wails v2 (跨平台桌面应用)
- **数据库**: SQLite (本地存储)
- **SSH库**: golang.org/x/crypto/ssh
- **配置管理**: 内置配置 + 数据库存储

## 2. 功能需求

### 2.1 核心功能
- **主机管理**: 添加、编辑、删除SSH连接配置，支持密码和密钥认证
- **检查规则**: 内置等保基线检查规则，支持启用/禁用控制
- **任务执行**: 创建检查任务，支持单台或批量主机检查
- **结果查看**: 显示检查结果，提供详细的检查报告
- **报告导出**: 支持PDF和HTML格式的报告导出

### 2.2 内置检查规则
- 身份鉴别类：用户账户、密码策略、登录控制等
- 访问控制类：文件权限、用户权限、sudo配置等  
- 安全审计类：审计日志、日志轮转、操作记录等
- 系统安全类：补丁更新、服务管理、内核参数等
- 网络安全类：防火墙、端口、SSH配置等

## 3. 界面设计

### 3.1 整体布局
```
┌─────────────────────────────────────────────────────────────────┐
│  🛡️ Linux等保基线核查工具 v1.0                    [⚙️][❌]     │
├─────────────────────────────────────────────────────────────────┤
│ 🏠仪表盘 | 🖥️主机管理 | 📋检查规则 | ⚡检查任务 | 📊报告中心    │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                        主工作区                                  │
│                     (简洁布局)                                   │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ 状态: 在线142台 | 运行中3个任务 | 最后检查: 2024-01-20 14:30    │
└─────────────────────────────────────────────────────────────────┘
```

### 3.2 主要页面

#### 3.2.1 仪表盘页面
- 系统概览：主机总数、在线状态、检查任务数、风险项目数
- 重要提醒：显示高危、中危、低危风险项目
- 快捷操作：创建任务、添加主机、生成报告、系统设置

#### 3.2.2 主机管理页面
- 主机列表：显示IP地址、主机名、状态、系统版本、最后检查时间
- 主机配置：主机名、IP、端口、用户名、认证方式、密码/密钥
- 操作功能：添加、编辑、删除、测试连接、批量导入

#### 3.2.3 检查规则页面
- 规则分类：按身份鉴别、访问控制、安全审计、系统安全、网络安全分类
- 规则列表：显示规则ID、名称、分类、风险等级、启用状态
- 规则控制：支持启用/禁用规则，查看规则详情

#### 3.2.4 检查任务页面
- 任务创建：任务名称、目标主机选择、检查规则选择
- 任务列表：显示任务时间、名称、主机数、状态、通过率
- 任务操作：查看详情、导出报告、重新执行

#### 3.2.5 报告中心页面
- 报告列表：显示生成时间、报告名称、主机数、通过率
- 报告预览：检查结果统计、主要问题分析
- 报告导出：支持PDF和HTML格式导出

## 4. 技术实现

### 4.1 前端技术
- 使用原生HTML5、CSS3、JavaScript，无任何框架依赖
- 简洁的CSS样式，响应式设计
- 模块化JavaScript代码组织
- 通过Wails与Go后端通信

### 4.2 后端技术
- Go语言实现SSH连接管理
- 内置等保基线检查规则
- SQLite数据库存储主机配置和检查结果
- 支持并发检查和任务管理

### 4.3 数据存储
- 主机配置表（hosts）
- 检查任务表（tasks）
- 检查结果表（results）
- 用户配置表（settings）
- 操作日志表（logs）

## 5. 非功能性需求

### 5.1 性能要求
- 支持同时检查50台主机
- 单台主机检查时间不超过3分钟
- 界面响应时间小于1秒

### 5.2 安全要求
- SSH连接信息加密存储
- 支持SSH密钥认证
- 操作日志完整记录

### 5.3 兼容性要求
- 支持主流Linux发行版（CentOS、Ubuntu、RHEL等）
- 支持Linux、Windows、macOS客户端

## 6. 设计理念

### 6.1 简化原则
- 专注核心功能，避免过度复杂化
- 内置标准规则，开箱即用
- 原生技术栈，无第三方依赖
- 简洁直观的用户界面

### 6.2 功能边界
- 仅提供等保基线检查功能
- 不支持自定义规则创建
- 不使用图表库，采用简单的文本和表格展示
- 专注于实用性和稳定性

## 7. 项目交付

### 7.1 软件交付
- 跨平台可执行程序
- 用户操作手册
- 部署说明文档

### 7.2 开发计划
- 第一阶段：基础框架搭建（1周）
- 第二阶段：核心功能开发（2周）
- 第三阶段：界面开发和集成（1周）
- 第四阶段：测试和优化（1周）
