# Linux等保基线核查工具需求文档（简化版）

## 1. 项目概述

### 1.1 项目背景
开发一款基于Wails GUI框架的Linux等保基线核查工具，通过SSH远程连接方式对目标Linux服务器进行安全基线检查。

### 1.2 项目目标
- 提供简洁的图形化界面
- 支持SSH远程执行核查命令
- 生成标准化的核查报告
- 内置等保基线检查规则

### 1.3 技术架构
- **前端技术**: HTML5 + CSS3 + JavaScript (原生)
- **后端语言**: Go 1.19+
- **GUI框架**: Wails v2 (跨平台桌面应用)
- **数据库**: SQLite (本地存储)
- **SSH库**: golang.org/x/crypto/ssh
- **配置管理**: 内置配置 + 数据库存储

## 2. 功能需求

### 2.1 核心功能
- **主机管理**: 添加、编辑、删除SSH连接配置，支持密码和密钥认证
- **检查规则**: 内置等保基线检查规则，支持启用/禁用控制
- **任务执行**: 创建检查任务，支持单台或批量主机检查
- **结果查看**: 显示检查结果，提供详细的检查报告
- **报告导出**: 支持PDF和HTML格式的报告导出

### 2.2 内置检查规则模板

#### 2.2.1 二级等保规则模板（基础版）
包含45项基础检查项：
- 身份鉴别：用户账户管理、密码策略、登录控制
- 访问控制：文件权限、用户权限、基础访问控制
- 安全审计：基础日志记录、审计配置
- 系统安全：基础安全配置、服务管理
- 网络安全：基础防火墙、端口管理

#### 2.2.2 三级等保规则模板（增强版）
包含78项增强检查项（包含二级+增强项）：
- 身份鉴别：增强密码策略、多因子认证、账户锁定
- 访问控制：强制访问控制、权限最小化、sudo增强配置
- 安全审计：完整审计链、日志完整性保护、实时监控
- 系统安全：内核加固、补丁管理、恶意代码防护
- 网络安全：网络隔离、入侵检测、SSL/TLS配置

#### 2.2.3 全量基线规则模板（完整版）
包含120项完整检查项（包含所有等保要求）：
- 涵盖二级、三级等保所有要求
- 额外的行业最佳实践检查项
- 高级安全配置检查
- 合规性验证检查

#### 2.2.4 自定义组合模板
- 支持从不同等保级别中选择特定检查项
- 可保存为自定义模板供重复使用
- 支持按业务需求定制检查范围

## 3. 界面设计

### 3.1 整体布局
```
┌─────────────────────────────────────────────────────────────────┐
│  🛡️ Linux等保基线核查工具 v1.0                    [⚙️][❌]     │
├─────────────────────────────────────────────────────────────────┤
│ 🏠仪表盘 | 🖥️主机管理 | 📋检查规则 | ⚡检查任务 | 📊报告中心    │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                        主工作区                                  │
│                     (简洁布局)                                   │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ 状态: 在线142台 | 运行中3个任务 | 最后检查: 2024-01-20 14:30    │
└─────────────────────────────────────────────────────────────────┘
```

### 3.2 主要页面

#### 3.2.1 仪表盘页面
- 系统概览：主机总数、在线状态、检查任务数、风险项目数
- 重要提醒：显示高危、中危、低危风险项目
- 快捷操作：创建任务、添加主机、生成报告、系统设置

#### 3.2.2 主机管理页面
- 主机列表：显示IP地址、主机名、状态、系统版本、最后检查时间
- 主机配置：主机名、IP、端口、用户名、认证方式、密码/密钥
- 操作功能：添加、编辑、删除、测试连接、批量导入

#### 3.2.3 检查规则页面
```
┌─────────────────────────────────────────────────────────────────┐
│ 📋 检查规则模板                                                  │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ [二级等保(45项)] [三级等保(78项)] [全量基线(120项)] [自定义] │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 当前选择：三级等保规则模板                                       │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 规则ID │ 检查项名称      │ 等保级别 │ 类型     │ 状态 │      │ │
│ ├─────────────────────────────────────────────────────────────┤ │
│ │ L2-001│ 用户账户唯一性   │ 二级    │ 身份鉴别 │ ✅   │      │ │
│ │ L2-002│ 密码复杂度策略   │ 二级    │ 身份鉴别 │ ✅   │      │ │
│ │ L3-001│ 账户锁定策略     │ 三级    │ 身份鉴别 │ ✅   │      │ │
│ │ L3-002│ 强制访问控制     │ 三级    │ 访问控制 │ ✅   │      │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 模板统计：共78项检查 | 二级基础:45项 | 三级增强:33项            │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.2.4 检查任务页面
```
┌─────────────────────────────────────────────────────────────────┐
│ ⚡ 检查任务                                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 任务名称: [生产环境等保检查]                                 │ │
│ │ 目标主机: [选择主机 ▼] 已选择: 15台                         │ │
│ │ 等保模板: (•)三级等保 ( )二级等保 ( )全量基线 ( )自定义      │ │
│ │ [创建任务] [保存模板]                                        │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 任务历史                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 时间     │任务名称    │主机数│等保级别│状态│通过率│操作    │ │
│ ├─────────────────────────────────────────────────────────────┤ │
│ │01-20 10:00│生产环境检查│15台 │三级等保│✅  │85%  │查看导出│ │
│ │01-19 18:00│测试环境检查│8台  │二级等保│✅  │92%  │查看导出│ │
│ │01-19 14:30│全量基线检查│3台  │全量基线│❌  │--   │查看重试│ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.2.5 报告中心页面
- 报告列表：显示生成时间、报告名称、主机数、通过率
- 报告预览：检查结果统计、主要问题分析
- 报告导出：支持PDF和HTML格式导出

## 4. 技术实现

### 4.1 前端技术
- 使用原生HTML5、CSS3、JavaScript，无任何框架依赖
- 简洁的CSS样式，响应式设计
- 模块化JavaScript代码组织
- 通过Wails与Go后端通信

### 4.2 后端技术
- Go语言实现SSH连接管理
- 内置等保规则模板（二级、三级、全量基线）
- 规则模板管理和动态加载
- SQLite数据库存储主机配置和检查结果
- 支持并发检查和任务管理

### 4.3 等保规则模板实现
```go
// 等保规则模板结构
type ComplianceTemplate struct {
    ID          string      `json:"id"`           // L2, L3, FULL
    Name        string      `json:"name"`         // 二级等保、三级等保、全量基线
    Level       int         `json:"level"`        // 2, 3, 0(全量)
    Description string      `json:"description"`  // 模板描述
    Rules       []CheckRule `json:"rules"`        // 包含的检查规则
    RuleCount   int         `json:"rule_count"`   // 规则数量
}

// 检查规则结构
type CheckRule struct {
    ID          string `json:"id"`          // L2-001, L3-001
    Name        string `json:"name"`        // 规则名称
    Level       int    `json:"level"`       // 等保级别 2/3
    Category    string `json:"category"`    // 身份鉴别、访问控制等
    Command     string `json:"command"`     // 检查命令
    Expected    string `json:"expected"`    // 期望结果
    Description string `json:"description"` // 规则描述
    Solution    string `json:"solution"`    // 整改建议
    Enabled     bool   `json:"enabled"`     // 是否启用
}
```

### 4.4 数据存储
- 主机配置表（hosts）
- 等保模板表（compliance_templates）
- 检查任务表（tasks）- 增加template_id字段
- 检查结果表（results）- 增加rule_level字段
- 用户配置表（settings）
- 操作日志表（logs）

## 5. 非功能性需求

### 5.1 性能要求
- 支持同时检查50台主机
- 单台主机检查时间不超过3分钟
- 界面响应时间小于1秒

### 5.2 安全要求
- SSH连接信息加密存储
- 支持SSH密钥认证
- 操作日志完整记录

### 5.3 兼容性要求
- 支持主流Linux发行版（CentOS、Ubuntu、RHEL等）
- 支持Linux、Windows、macOS客户端

## 6. 设计理念

### 6.1 简化原则
- 专注核心功能，避免过度复杂化
- 按等保级别组织规则，符合实际使用场景
- 内置标准模板，开箱即用
- 原生技术栈，无第三方依赖
- 简洁直观的用户界面

### 6.2 等保模板设计优势
- **标准化**: 严格按照国家等保标准设计规则模板
- **分级管理**: 二级、三级等保规则清晰分离，便于选择
- **完整覆盖**: 全量基线模板涵盖所有等保要求
- **易于使用**: 用户只需选择等保级别，无需了解技术细节
- **合规保证**: 确保检查结果符合等保合规要求

### 6.3 功能边界
- 仅提供等保基线检查功能
- 按等保级别提供标准模板
- 不使用图表库，采用简单的文本和表格展示
- 专注于实用性和合规性

## 7. 项目交付

### 7.1 软件交付
- 跨平台可执行程序
- 用户操作手册
- 部署说明文档

### 7.2 开发计划
- 第一阶段：基础框架搭建（1周）
- 第二阶段：核心功能开发（2周）
- 第三阶段：界面开发和集成（1周）
- 第四阶段：测试和优化（1周）
