# Linux等保基线核查工具需求文档

## 1. 项目概述

### 1.1 项目背景
根据《网络安全等级保护基本要求》，需要开发一款基于Wails GUI框架的Linux等保基线核查工具，通过SSH远程连接方式对目标Linux服务器进行安全基线检查，确保系统符合等级保护要求。

### 1.2 项目目标
- 提供图形化界面的等保基线核查工具
- 支持SSH远程执行核查命令，无需在目标服务器安装代理
- 生成标准化的核查报告
- 支持批量检查和定时任务
- 提供可扩展的检查规则配置

### 1.3 技术架构
- **前端技术**: HTML5 + CSS3 + JavaScript (原生)
- **后端语言**: Go 1.19+
- **GUI框架**: Wails v2 (跨平台桌面应用)
- **数据库**: SQLite (本地存储)
- **SSH库**: golang.org/x/crypto/ssh
- **配置管理**: 内置配置 + 数据库存储
- **UI组件**: 简洁的原生CSS样式

## 2. 功能需求

### 2.1 核心功能
- **主机管理**: 添加、编辑、删除SSH连接配置，支持密码和密钥认证
- **检查规则**: 内置等保基线检查规则，支持启用/禁用控制
- **任务执行**: 创建检查任务，支持单台或批量主机检查
- **结果查看**: 显示检查结果，提供详细的检查报告
- **报告导出**: 支持PDF和HTML格式的报告导出

### 2.2 内置检查规则
- 身份鉴别类：用户账户、密码策略、登录控制等
- 访问控制类：文件权限、用户权限、sudo配置等
- 安全审计类：审计日志、日志轮转、操作记录等
- 系统安全类：补丁更新、服务管理、内核参数等
- 网络安全类：防火墙、端口、SSH配置等

## 3. 非功能性需求

### 3.1 性能要求
- 支持同时检查100台主机
- 单台主机检查时间不超过5分钟
- 界面响应时间小于2秒
- 内存占用不超过500MB

### 3.2 安全要求
- SSH连接信息加密存储
- 支持SSH密钥认证
- 操作日志完整记录
- 敏感信息脱敏显示
- 连接超时和重试限制

### 3.3 可用性要求
- 7×24小时稳定运行
- 异常自动恢复机制
- 友好的错误提示信息
- 完善的帮助文档

### 3.4 兼容性要求
- 支持主流Linux发行版（CentOS、Ubuntu、RHEL、SUSE等）
- 支持Linux、Windows、macOS客户端
- 支持SSH协议v2

## 4. 界面设计要求

### 4.1 整体布局设计
```
┌─────────────────────────────────────────────────────────────────┐
│  🛡️ Linux等保基线核查工具 v1.0                    [⚙️][❌]     │
├─────────────────────────────────────────────────────────────────┤
│ 🏠仪表盘 | 🖥️主机管理 | 📋检查规则 | ⚡检查任务 | 📊报告中心    │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                        主工作区                                  │
│                     (简洁布局)                                   │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ 状态: 在线142台 | 运行中3个任务 | 最后检查: 2024-01-20 14:30    │
└─────────────────────────────────────────────────────────────────┘
```

### 4.2 主要页面设计

#### 4.2.1 仪表盘页面
```
┌─────────────────────────────────────────────────────────────────┐
│ � 系统概览                                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 主机总数: 156台 | 在线: 142台 | 检查任务: 23个 | 风险: 8项   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 🚨 重要提醒                                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • ************ - root账户使用弱密码 (高危)                  │ │
│ │ • 192.168.1.15 - SSH允许root登录 (中危)                     │ │
│ │ • ************ - 系统补丁更新滞后 (低危)                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ⚡ 快捷操作                                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ [➕ 创建检查任务] [🖥️ 添加主机] [📊 生成报告] [⚙️ 系统设置] │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.2 主机管理页面
```
┌─────────────────────────────────────────────────────────────────┐
│ �️ 主机管理                                [➕添加] [�导入]    │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ IP地址      │ 主机名  │ 状态 │ 系统版本    │ 最后检查      │ │
│ ├─────────────────────────────────────────────────────────────┤ │
│ │ ************│ web01   │ �   │ CentOS 7.9  │ 2024-01-20   │ │
│ │ ************│ web02   │ 🔴   │ Ubuntu 20.04│ 连接失败      │ │
│ │ ************│ db01    │ 🟢   │ RHEL 8.5    │ 2024-01-20   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 主机详情                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 主机名: [web01        ] IP: [************] 端口: [22]      │ │
│ │ 用户名: [root         ] 认证: (•)密码 ( )密钥               │ │
│ │ 密码:   [**********  ] [测试连接] [保存] [删除]             │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.3 检查任务页面
```
┌─────────────────────────────────────────────────────────────────┐
│ 🖥️ 主机管理                        [📁分组] [➕添加] [📥导入]    │
│ ┌─────────────┬─────────────────────────────────┬─────────────┐ │
│ │ 主机分组树   │         主机列表                │  主机详情   │ │
│ │ ┌─────────┐ │ ┌─────────────────────────────┐ │ ┌─────────┐ │ │
│ │ │📁生产环境│ │ │IP地址│主机名│状态│CPU│内存│ │ │ │ web01   │ │ │
│ │ │ ├Web(15)│ │ ├─────────────────────────────┤ │ │ 详细信息 │ │ │
│ │ │ ├DB(5)  │ │ │.1.10│web01│🟢│45%│2.1G│ │ │ │         │ │ │
│ │ │ └Cache │ │ │.1.11│web02│🔴│--│-- │ │ │ │ 🟢 在线  │ │ │
│ │ │📁测试环境│ │ │.1.20│db01 │🟢│78%│4.5G│ │ │ │ 💾 8GB  │ │ │
│ │ │ └Test(8)│ │ │.1.21│db02 │�│89%│7.2G│ │ │ │ 🖥️ 4核   │ │ │
│ │ │📁开发环境│ │ └─────────────────────────────┘ │ │ 🌐 CentOS│ │ │
│ │ │ └Dev(12)│ │                                 │ │         │ │ │
│ │ └─────────┘ │ [全选] [批量操作▼] [刷新状态]   │ │ [编辑]  │ │ │
│ └─────────────┴─────────────────────────────────┴─────────────┘ │
│                                                                 │
│ 快速操作面板                                                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 📊 统计: 总计40台 | 🟢在线35台 | 🔴离线3台 | 🟡警告2台        │ │
│ │ ⚡ 快捷: [批量检查] [导出列表] [健康检查] [性能监控]          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 主机配置编辑器 (弹窗/抽屉式)                                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 基本信息                                                     │ │
│ │ 主机名: [web01        ] IP: [************] 端口: [22]      │ │
│ │ 分组:   [Web服务器 ▼  ] 标签: [生产,nginx,前端]            │ │
│ │                                                             │ │
│ │ 连接配置                                                     │ │
│ │ 用户名: [root         ] 认证: (•)密码 ( )密钥 ( )证书       │ │
│ │ 密码:   [**********  ] 密钥: [选择文件]                    │ │
│ │                                                             │ │
│ │ 高级选项                                                     │ │
│ │ 连接超时: [30秒] 重试次数: [3次] 保持连接: [☑]              │ │
│ │                                                             │ │
│ │ [测试连接] [保存] [取消]                                     │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.3 检查规则页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 📋 检查规则                                [🔄刷新] [📊统计]     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 规则分类筛选                                                 │ │
│ │ [全部] [身份鉴别] [访问控制] [安全审计] [系统安全] [网络安全] │ │
│ │ 搜索: [输入关键词...] [🔍]                                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 规则列表                                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 规则ID │ 规则名称        │ 分类     │ 风险等级 │ 状态 │ 操作 │ │
│ ├─────────────────────────────────────────────────────────────┤ │
│ │ R001  │ 用户账户唯一性   │ 身份鉴别 │ 高      │ ✅   │ 详情 │ │
│ │ R002  │ 密码复杂度策略   │ 身份鉴别 │ 高      │ ✅   │ 详情 │ │
│ │ R003  │ 文件权限检查     │ 访问控制 │ 中      │ ✅   │ 详情 │ │
│ │ R004  │ 审计日志配置     │ 安全审计 │ 高      │ ❌   │ 详情 │ │
│ │ R005  │ 防火墙配置检查   │ 网络安全 │ 中      │ ✅   │ 详情 │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 规则详情面板                                                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 规则名称: 用户账户唯一性检查                                 │ │
│ │ 规则ID: R001                                                │ │
│ │ 分类: 身份鉴别类                                             │ │
│ │ 风险等级: 高                                                 │ │
│ │                                                             │ │
│ │ 检查描述:                                                    │ │
│ │ 检查系统中是否存在重复的用户账户，确保每个用户账户的唯一性   │ │
│ │                                                             │ │
│ │ 检查命令:                                                    │ │
│ │ cut -d: -f1 /etc/passwd | sort | uniq -d                   │ │
│ │                                                             │ │
│ │ 整改建议:                                                    │ │
│ │ 删除重复的用户账户，确保系统中每个用户名唯一                 │ │
│ │                                                             │ │
│ │ [启用规则] [禁用规则]                                        │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.4 检查任务页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│  ⚡ 检查任务                                                      │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 创建新任务                                                   ││
│  │ 任务名称: [生产环境安全检查_20240120]                        ││
│  │ 目标主机: [选择主机组 ▼] [已选择: 15台主机]                  ││
│  │ 检查规则: [☑等保基线全套] [☑身份鉴别类] [☐访问控制类]       ││
│  │ 执行时间: (•) 立即执行 ( ) 定时执行 [2024-01-20 14:00]       ││
│  │ [创建任务] [保存模板]                                         ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  正在执行的任务                                                  │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 任务: 生产环境检查 | 进度: ████████░░ 80% | 12/15台完成      ││
│  │ 当前: 正在检查192.168.1.15 - 网络安全配置                   ││
│  │ [暂停] [停止] [查看详情]                                     ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  任务历史                                                        │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 时间        │任务名称      │主机数│状态  │通过率│操作      ││
│  ├─────────────────────────────────────────────────────────────┤│
│  │2024-01-20 10:00│生产环境检查│15台│✅完成│85%│[查看][导出]││
│  │2024-01-19 18:00│测试环境检查│8台 │✅完成│92%│[查看][导出]││
│  │2024-01-19 14:30│DB服务器检查│3台 │❌失败│--│[查看][重试]││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.5 报告中心页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│  📊 报告中心                                                     │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 筛选: 时间: [最近7天▼] 状态: [全部▼] [应用筛选]              ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  报告列表                                                        │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 生成时间      │报告名称        │主机数│通过率│操作          ││
│  ├─────────────────────────────────────────────────────────────┤│
│  │2024-01-20 10:30│生产环境安全报告│15台  │85%  │[查看][导出]  ││
│  │2024-01-19 18:15│测试环境安全报告│8台   │92%  │[查看][导出]  ││
│  │2024-01-19 14:45│数据库安全报告  │3台   │78%  │[查看][导出]  ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  报告详情预览                                                    │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 📈 检查结果统计                                              ││
│  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐            ││
│  │ │ 通过项目 │ │ 警告项目 │ │ 失败项目 │ │ 总检查项 │            ││
│  │ │   127   │ │   18    │ │    8    │ │   153   │            ││
│  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘            ││
│  │                                                             ││
│  │ 🔍 主要问题分析                                              ││
│  │ • 身份鉴别类: 3台主机存在弱密码问题                          ││
│  │ • 访问控制类: 2台主机文件权限配置不当                        ││
│  │ • 安全审计类: 5台主机审计日志配置缺失                        ││
│  │                                                             ││
│  │ [导出PDF] [导出HTML]                                        ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### 4.3 前端技术实现要求

#### 4.3.1 原生技术栈说明
- **HTML5**: 使用语义化标签，无需任何框架
- **CSS3**: 原生CSS实现，不使用预处理器
- **JavaScript**: 纯原生ES6+，不依赖任何前端框架
- **优势**: 轻量级、高性能、无依赖、易维护

#### 4.3.2 HTML结构设计
- 语义化HTML5标签
- 模块化组件结构
- 无障碍访问支持(ARIA)
- 简洁清晰的DOM结构

#### 4.3.3 CSS样式设计
- 采用CSS3 Flexbox/Grid布局
- 响应式设计，支持不同分辨率
- CSS变量管理主题色彩
- 动画效果增强用户体验
- 组件化CSS架构
- 不使用任何CSS框架或预处理器

#### 4.3.4 JavaScript功能实现
- 原生ES6+语法，无需Babel转译
- 模块化代码组织（ES6 Modules）
- 事件驱动的交互逻辑
- 异步数据处理（async/await）
- 本地存储管理
- 不使用任何JavaScript框架或第三方库

#### 4.3.5 原生图表实现
- **CSS进度条**: 使用CSS3动画实现进度条和百分比显示
- **SVG图表**: 使用原生SVG绘制饼图、环形图等
- **Canvas图表**: 使用HTML5 Canvas绘制折线图、柱状图
- **CSS Grid布局**: 实现数据表格和统计卡片
- **动画效果**: 使用CSS3 transition和animation

#### 4.3.6 原生UI组件库设计
```css
/* 主要色彩方案 */
:root {
  --primary-color: #2563eb;      /* 主色调-蓝色 */
  --success-color: #10b981;      /* 成功-绿色 */
  --warning-color: #f59e0b;      /* 警告-橙色 */
  --danger-color: #ef4444;       /* 危险-红色 */
  --background-color: #f8fafc;   /* 背景色 */
  --sidebar-color: #1e293b;      /* 侧边栏色 */
  --text-primary: #1f2937;       /* 主文字色 */
  --text-secondary: #6b7280;     /* 次文字色 */
  --border-color: #e5e7eb;       /* 边框色 */
}

/* 按钮组件 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-primary { background: var(--primary-color); color: white; }
.btn-success { background: var(--success-color); color: white; }
.btn-warning { background: var(--warning-color); color: white; }
.btn-danger { background: var(--danger-color); color: white; }

/* 表格组件 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 状态指示器 */
.status-online { color: var(--success-color); }
.status-offline { color: var(--danger-color); }
.status-warning { color: var(--warning-color); }

/* 原生进度条组件 */
.progress-bar {
  width: 100%;
  height: 20px;
  background: #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  transition: width 0.3s ease;
  border-radius: 10px;
}

/* 原生环形进度图 */
.circle-progress {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(var(--success-color) 0deg 252deg, #e5e7eb 252deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-progress::before {
  content: '';
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 50%;
  position: absolute;
}

.circle-progress .percentage {
  position: relative;
  z-index: 1;
  font-size: 18px;
  font-weight: bold;
}
```

### 4.4 用户体验要求
- 界面简洁直观，符合现代设计规范
- 操作流程清晰，减少用户学习成本
- 支持键盘快捷键操作
- 响应式设计，适配不同屏幕尺寸
- 支持明暗主题切换
- 加载状态和进度反馈
- 友好的错误提示和帮助信息

## 5. 数据存储设计

### 5.1 数据库表结构
- 主机配置表（hosts）
- 检查任务表（tasks）
- 检查结果表（results）
- 用户配置表（settings）
- 操作日志表（logs）

### 5.2 数据安全
- 敏感数据加密存储
- 定期数据备份
- 数据完整性校验
- 支持数据导入导出

## 6. 内置配置设计理念

### 6.1 设计原则
- **开箱即用**: 软件安装后无需额外配置即可使用
- **安全可靠**: 避免外部配置文件被恶意修改的风险
- **简化部署**: 减少配置文件管理的复杂性
- **标准化**: 内置符合等保要求的标准检查规则

### 6.2 内置规则管理
#### 6.2.1 标准规则内置
- 所有等保基线检查规则内置在程序中
- 规则以Go代码形式定义，编译时嵌入
- 支持规则分类和优先级设置
- 内置规则版本控制和更新机制

#### 6.2.2 规则数据结构
```go
type CheckRule struct {
    ID          string            `json:"id"`
    Name        string            `json:"name"`
    Category    string            `json:"category"`
    Level       string            `json:"level"`      // 高、中、低
    Command     string            `json:"command"`    // 检查命令
    Expected    string            `json:"expected"`   // 期望结果
    Pattern     string            `json:"pattern"`    // 匹配模式
    Description string            `json:"description"`
    Solution    string            `json:"solution"`   // 整改建议
    Enabled     bool              `json:"enabled"`
    Metadata    map[string]string `json:"metadata"`
}
```

### 6.3 配置管理策略
- **系统配置**: 通过GUI界面设置，存储在数据库中
- **用户偏好**: 界面主题、语言等个性化设置
- **连接配置**: SSH连接信息加密存储在数据库中
- **规则配置**: 完全内置，无需外部配置

### 6.4 内置配置的优势
#### 6.4.1 部署简化
- 无需准备和维护外部配置文件
- 减少部署错误和配置遗漏
- 支持一键安装和即时使用
- 降低运维复杂度

#### 6.4.2 安全性提升
- 避免配置文件被恶意篡改
- 减少敏感信息泄露风险
- 内置规则经过严格测试和验证
- 符合安全最佳实践

#### 6.4.3 用户体验优化
- 开箱即用，无需学习配置语法
- 标准化的检查规则，确保一致性
- 减少配置错误导致的功能异常
- 简化操作流程，专注核心功能

### 6.5 简化设计理念
#### 6.5.1 核心原则
- **专注核心功能**: 专注于等保基线检查，不提供过度复杂的自定义功能
- **标准化优先**: 内置符合等保要求的标准规则，确保检查结果的权威性
- **简化操作**: 减少用户配置负担，提供直观的操作界面
- **技术简洁**: 使用原生前端技术，避免复杂的技术栈

#### 6.5.2 功能边界
- **内置规则**: 提供完整的等保基线检查规则，无需用户自定义
- **规则控制**: 仅支持启用/禁用内置规则，不支持修改规则内容
- **标准报告**: 提供标准化的检查报告格式
- **简化配置**: 最小化配置项，重点关注主机管理和任务执行
- **原生图表**: 使用CSS、SVG、Canvas实现图表，无第三方依赖

#### 6.5.3 原生图表实现优势
- **零依赖**: 无需引入任何第三方图表库
- **轻量级**: 减少应用体积和加载时间
- **高性能**: 原生实现，性能更优
- **可控性**: 完全自主控制图表样式和行为
- **安全性**: 避免第三方库的安全风险
- **维护性**: 减少依赖更新和兼容性问题

## 7. 技术实现要点

### 7.1 前端技术实现

#### 7.1.1 项目结构设计
```
frontend/
├── index.html              # 主页面入口
├── css/
│   ├── main.css            # 主样式文件
│   ├── components.css      # 组件样式
│   ├── themes.css          # 主题样式
│   └── responsive.css      # 响应式样式
├── js/
│   ├── main.js             # 主应用逻辑
│   ├── api.js              # API通信模块
│   ├── components/         # UI组件
│   │   ├── table.js        # 表格组件
│   │   ├── form.js         # 表单组件
│   │   ├── progress.js     # 进度条组件
│   │   ├── charts.js       # 原生图表组件
│   │   └── modal.js        # 模态框组件
│   ├── pages/              # 页面模块
│   │   ├── dashboard.js    # 仪表盘页面
│   │   ├── hosts.js        # 主机管理页面
│   │   ├── rules.js        # 检查规则页面
│   │   ├── tasks.js        # 检查任务页面
│   │   └── reports.js      # 报告中心页面
│   └── utils/              # 工具函数
│       ├── storage.js      # 本地存储
│       ├── validator.js    # 数据验证
│       └── formatter.js    # 数据格式化
├── assets/
│   ├── icons/              # 图标文件
│   ├── images/             # 图片资源
│   └── fonts/              # 字体文件
└── lib/
    └── (无第三方库依赖)     # 完全原生实现
```

#### 7.1.2 核心JavaScript模块
```javascript
// main.js - 主应用入口
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupNavigation();
        this.loadPage(this.currentPage);
        this.setupEventListeners();
    }

    setupNavigation() {
        // 导航菜单事件绑定
    }

    loadPage(pageName) {
        // 动态加载页面内容
    }
}

// api.js - 与Go后端通信
class API {
    static async call(method, params) {
        // 通过Wails调用Go后端方法
        return window.go.main.App[method](params);
    }

    static async getHosts() {
        return this.call('GetHosts');
    }

    static async getRules() {
        return this.call('GetRules');
    }

    static async toggleRule(ruleId, enabled) {
        return this.call('ToggleRule', { ruleId, enabled });
    }

    static async createTask(taskData) {
        return this.call('CreateTask', taskData);
    }
}

// charts.js - 原生图表组件
class NativeCharts {
    // 创建进度条
    static createProgressBar(container, percentage, label) {
        const progressHTML = `
            <div class="progress-container">
                <div class="progress-label">${label}</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${percentage}%"></div>
                </div>
                <div class="progress-text">${percentage}%</div>
            </div>
        `;
        container.innerHTML = progressHTML;
    }

    // 创建环形进度图
    static createCircleProgress(container, percentage, label) {
        const degree = (percentage / 100) * 360;
        const circleHTML = `
            <div class="circle-progress" style="background: conic-gradient(var(--success-color) 0deg ${degree}deg, #e5e7eb ${degree}deg 360deg);">
                <div class="percentage">${percentage}%</div>
            </div>
            <div class="circle-label">${label}</div>
        `;
        container.innerHTML = circleHTML;
    }

    // 使用Canvas创建折线图
    static createLineChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        const ctx = canvas.getContext('2d');
        const { width, height } = canvas;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 绘制折线图逻辑
        // ... 具体实现
    }

    // 使用SVG创建饼图
    static createPieChart(container, data, options = {}) {
        const total = data.reduce((sum, item) => sum + item.value, 0);
        let currentAngle = 0;

        const svgHTML = data.map(item => {
            const percentage = (item.value / total) * 100;
            const angle = (item.value / total) * 360;
            const path = this.createPieSlice(currentAngle, angle);
            currentAngle += angle;

            return `<path d="${path}" fill="${item.color}" />`;
        }).join('');

        container.innerHTML = `<svg viewBox="0 0 200 200">${svgHTML}</svg>`;
    }
}
```

### 7.2 SSH连接管理
- 连接池管理，复用连接
- 超时和重试机制
- 并发连接数控制
- 连接状态监控

### 7.3 命令执行引擎
- 命令模板化管理
- 结果解析和匹配
- 异常处理机制
- 执行日志记录

### 7.4 规则引擎设计
- 内置标准等保检查规则，无需外部配置文件
- 支持条件判断和逻辑运算
- 结果评分算法
- 规则依赖关系处理
- 规则版本管理和更新机制

## 8. 项目交付物

### 7.5 报告导出功能设计

#### 7.5.1 PDF导出
- 使用Go PDF库生成标准PDF格式
- 支持中文字体和图表嵌入
- 包含完整的检查结果和统计信息
- 适合正式文档归档和打印

#### 7.5.2 HTML导出
- 生成独立的HTML文件，包含CSS样式
- 支持交互式图表和数据展示
- 响应式设计，适配不同设备查看
- 包含JavaScript交互功能（如数据筛选、排序）
- 可在任何现代浏览器中打开查看
- 支持自定义主题和样式模板

#### 7.5.3 导出功能特性
- 批量导出多个报告
- 自定义导出内容和格式
- 导出进度显示和取消功能
- 导出文件自动命名和路径管理

### 8.1 软件交付
- 跨平台可执行程序
- 安装包和部署文档
- 用户操作手册
- 管理员配置指南

### 8.2 文档交付
- 详细设计文档
- API接口文档
- 测试报告
- 运维手册

## 9. 项目计划

### 9.1 开发阶段
- 第一阶段：基础框架搭建（2周）
- 第二阶段：核心功能开发（4周）
- 第三阶段：界面开发和集成（3周）
- 第四阶段：测试和优化（2周）

### 9.2 里程碑
- M1：完成项目架构设计和技术选型
- M2：完成SSH连接和基础检查功能
- M3：完成GUI界面和用户交互
- M4：完成系统测试和文档编写

## 10. 风险评估

### 10.1 技术风险
- SSH连接稳定性问题
- 不同Linux发行版兼容性
- 大规模并发检查性能瓶颈

### 10.2 缓解措施
- 充分的兼容性测试
- 性能压力测试
- 异常处理机制完善
- 分阶段交付降低风险
