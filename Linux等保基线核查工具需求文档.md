# Linux等保基线核查工具需求文档

## 1. 项目概述

### 1.1 项目背景
根据《网络安全等级保护基本要求》，需要开发一款基于Wails GUI框架的Linux等保基线核查工具，通过SSH远程连接方式对目标Linux服务器进行安全基线检查，确保系统符合等级保护要求。

### 1.2 项目目标
- 提供图形化界面的等保基线核查工具
- 支持SSH远程执行核查命令，无需在目标服务器安装代理
- 生成标准化的核查报告
- 支持批量检查和定时任务
- 提供可扩展的检查规则配置

### 1.3 技术架构
- **前端技术**: HTML5 + CSS3 + JavaScript (原生)
- **后端语言**: Go 1.19+
- **GUI框架**: Wails v2 (跨平台桌面应用)
- **数据库**: SQLite (本地存储)
- **SSH库**: golang.org/x/crypto/ssh
- **配置格式**: YAML/JSON
- **UI组件**: 自定义CSS组件库
- **图表库**: Chart.js (报告可视化)

## 2. 功能需求

### 2.1 主机管理模块
#### 2.1.1 连接配置管理
- 添加/编辑/删除SSH连接配置
- 支持密码和密钥两种认证方式
- 连接信息加密存储
- 支持连接测试功能
- 批量导入主机配置（CSV/Excel格式）

#### 2.1.2 主机分组管理
- 支持主机分组功能
- 按环境、业务、地域等维度分组
- 支持多级分组结构

### 2.2 检查规则模块
#### 2.2.1 内置检查规则
**身份鉴别类检查项**:
- 用户账户唯一性检查
- 密码复杂度策略检查
- 账户锁定策略检查
- 登录失败处理机制检查
- 特权用户管理检查

**访问控制类检查项**:
- 文件和目录权限检查
- 关键系统文件权限检查
- 用户权限最小化检查
- sudo配置检查
- 远程访问控制检查

**安全审计类检查项**:
- 系统审计功能启用检查
- 审计日志配置检查
- 日志轮转配置检查
- 关键操作审计检查
- 日志完整性保护检查

**系统安全类检查项**:
- 系统补丁更新状态检查
- 不必要服务关闭检查
- 内核参数安全配置检查
- 系统资源限制检查
- 恶意代码防护检查

**网络安全类检查项**:
- 防火墙配置检查
- 网络服务端口检查
- SSH配置安全检查
- 网络参数配置检查
- SSL/TLS配置检查

#### 2.2.2 自定义规则管理
- 支持YAML格式的规则配置
- 规则分组和分类管理
- 规则启用/禁用控制
- 自定义检查脚本支持
- 规则导入/导出功能

### 2.3 检查任务模块
#### 2.3.1 任务创建和管理
- 创建一次性检查任务
- 创建定时检查任务
- 任务模板管理
- 任务执行历史记录
- 任务状态监控

#### 2.3.2 批量检查功能
- 支持多主机并发检查
- 检查进度实时显示
- 异常处理和重试机制
- 检查结果实时反馈

### 2.4 报告生成模块
#### 2.4.1 报告内容
- 检查结果汇总统计
- 详细检查项结果
- 风险等级评估
- 整改建议
- 合规性评分

#### 2.4.2 报告格式
- 支持PDF格式导出
- 支持HTML格式导出
- 支持HTML在线查看
- 自定义报告模板
- 报告数据可视化图表

### 2.5 系统管理模块
#### 2.5.1 用户管理
- 本地用户账户管理
- 操作权限控制
- 操作日志记录

#### 2.5.2 系统配置
- 全局参数配置
- 日志级别设置
- 数据备份和恢复
- 系统更新管理

## 3. 非功能性需求

### 3.1 性能要求
- 支持同时检查100台主机
- 单台主机检查时间不超过5分钟
- 界面响应时间小于2秒
- 内存占用不超过500MB

### 3.2 安全要求
- SSH连接信息加密存储
- 支持SSH密钥认证
- 操作日志完整记录
- 敏感信息脱敏显示
- 连接超时和重试限制

### 3.3 可用性要求
- 7×24小时稳定运行
- 异常自动恢复机制
- 友好的错误提示信息
- 完善的帮助文档

### 3.4 兼容性要求
- 支持主流Linux发行版（CentOS、Ubuntu、RHEL、SUSE等）
- 支持Linux、Windows、macOS客户端
- 支持SSH协议v2

## 4. 界面设计要求

### 4.1 主界面布局设计

#### 4.1.1 优化后的整体布局结构
```
┌─────────────────────────────────────────────────────────────────┐
│  🛡️ Linux等保基线核查工具 v1.0        👤admin [⚙️][❌]        │ 60px
├─────────────────────────────────────────────────────────────────┤
│ 🏠仪表盘 > 系统概览              [📊导出] [🔄刷新] [➕新建]     │ 40px
├─────┬───────────────────────────────────────────────┬───────────┤
│ 🏠  │                                               │           │
│ 🖥️  │                                               │   右侧    │
│ 📋  │              主工作区                          │   详情    │
│ ⚡  │           (卡片式布局)                        │   面板    │
│ 📊  │                                               │ (可选显示) │
│ ⚙️  │                                               │           │
├─────┴───────────────────────────────────────────────┴───────────┤
│ 🟢在线:142台 | ⚡运行中:3个任务 | 💾内存:2.1GB | 🕐14:30:25    │ 50px
└─────────────────────────────────────────────────────────────────┘
```

**布局特点**:
- **可折叠侧边栏**: 200px(展开) / 60px(折叠)，支持图标模式
- **顶部工具栏**: 面包屑导航 + 快捷操作按钮
- **右侧详情面板**: 300px宽度，可选显示详细信息
- **增强状态栏**: 实时系统状态和资源监控
- **响应式设计**: 自适应不同屏幕尺寸

#### 4.1.2 智能侧边栏设计

**展开状态 (200px)**:
```
┌─────────────────────┐
│ 🏠 仪表盘            │ ← 当前页面高亮
├─────────────────────┤
│ 🖥️ 主机管理          │
│   • 主机列表 (15)    │ ← 显示数量
│   • 主机分组 (5)     │
│   • 连接配置         │
├─────────────────────┤
│ 📋 检查规则          │
│   • 内置规则 (45)    │
│   • 自定义规则 (12)  │
│   • 规则管理         │
├─────────────────────┤
│ ⚡ 检查任务          │
│   • 创建任务         │
│   • 任务监控 🔴      │ ← 状态指示器
│   • 任务历史         │
├─────────────────────┤
│ 📊 报告中心          │
│   • 报告列表 (28)    │
│   • 统计分析         │
│   • 报告模板         │
├─────────────────────┤
│ ⚙️ 系统设置          │
│   • 全局配置         │
│   • 用户管理         │
│   • 日志查看         │
└─────────────────────┘
```

**折叠状态 (60px)**:
```
┌─────┐
│ 🏠  │ ← 仅显示图标
├─────┤
│ 🖥️  │
├─────┤
│ 📋  │
├─────┤
│ ⚡  │ 🔴 ← 状态点
├─────┤
│ 📊  │
├─────┤
│ ⚙️  │
└─────┘
```

### 4.2 关键页面设计
#### 4.2.1 优化后的仪表盘页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 📊 系统概览                                    [📅今日] [🔄实时] │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │ │
│ │ │主机总数  │ │在线主机  │ │检查任务  │ │风险项目  │ │合规率   │ │ │
│ │ │  156    │ │ 142 📈  │ │ 23 🔄  │ │  8 🚨  │ │ 87.5%  │ │ │
│ │ │ +5 本周 │ │ 91.0%   │ │ 3运行中  │ │ 高:2   │ │ +2.1%  │ │ │
│ │ └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘ │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────┐ ┌─────────────────────────────────┐ │
│ │ 📈 检查趋势分析          │ │ 🎯 实时任务监控                  │ │
│ │ ┌─────────────────────┐ │ │ ┌─────────────────────────────┐ │ │
│ │ │    [动态折线图]      │ │ │ │ ✅ 生产环境检查 (15/15)     │ │ │
│ │ │  通过率: 87.5% ↗    │ │ │ │ 🔄 测试环境检查 (8/12)      │ │ │
│ │ │  风险数: 8个 ↘      │ │ │ │ ⏳ 开发环境检查 (等待中)     │ │ │
│ │ │  [7天/30天/90天]    │ │ │ │ ❌ DB集群检查 (连接失败)    │ │ │
│ │ └─────────────────────┘ │ │ └─────────────────────────────┘ │ │
│ └─────────────────────────┘ └─────────────────────────────────┘ │
│                                                                 │
│ 🚨 紧急风险提醒                              [查看全部] [忽略] │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 🔴 高危 | ************ | root账户使用弱密码    [立即处理] │ │
│ │ 🟠 中危 | 192.168.1.15 | SSH允许root登录       [查看详情] │ │
│ │ 🟡 低危 | 192.168.1.20 | 系统补丁更新滞后      [计划更新] │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─────────────────────────┐ ┌─────────────────────────────────┐ │
│ │ 🏆 合规性评分            │ │ 📋 快捷操作                      │ │
│ │ ┌─────────────────────┐ │ │ ┌─────────────────────────────┐ │ │
│ │ │   [环形进度图]       │ │ │ │ ➕ 创建检查任务              │ │ │
│ │ │      87.5%          │ │ │ │ 🖥️ 添加主机                 │ │ │
│ │ │   等保三级合规       │ │ │ │ 📊 生成报告                 │ │ │
│ │ │                     │ │ │ │ ⚙️ 系统设置                 │ │ │
│ │ └─────────────────────┘ │ │ └─────────────────────────────┘ │ │
│ └─────────────────────────┘ └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.2 优化后的主机管理页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│ 🖥️ 主机管理                        [📁分组] [➕添加] [📥导入]    │
│ ┌─────────────┬─────────────────────────────────┬─────────────┐ │
│ │ 主机分组树   │         主机列表                │  主机详情   │ │
│ │ ┌─────────┐ │ ┌─────────────────────────────┐ │ ┌─────────┐ │ │
│ │ │📁生产环境│ │ │IP地址│主机名│状态│CPU│内存│ │ │ │ web01   │ │ │
│ │ │ ├Web(15)│ │ ├─────────────────────────────┤ │ │ 详细信息 │ │ │
│ │ │ ├DB(5)  │ │ │.1.10│web01│🟢│45%│2.1G│ │ │ │         │ │ │
│ │ │ └Cache │ │ │.1.11│web02│🔴│--│-- │ │ │ │ 🟢 在线  │ │ │
│ │ │📁测试环境│ │ │.1.20│db01 │🟢│78%│4.5G│ │ │ │ 💾 8GB  │ │ │
│ │ │ └Test(8)│ │ │.1.21│db02 │�│89%│7.2G│ │ │ │ 🖥️ 4核   │ │ │
│ │ │📁开发环境│ │ └─────────────────────────────┘ │ │ 🌐 CentOS│ │ │
│ │ │ └Dev(12)│ │                                 │ │         │ │ │
│ │ └─────────┘ │ [全选] [批量操作▼] [刷新状态]   │ │ [编辑]  │ │ │
│ └─────────────┴─────────────────────────────────┴─────────────┘ │
│                                                                 │
│ 快速操作面板                                                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 📊 统计: 总计40台 | 🟢在线35台 | 🔴离线3台 | 🟡警告2台        │ │
│ │ ⚡ 快捷: [批量检查] [导出列表] [健康检查] [性能监控]          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 主机配置编辑器 (弹窗/抽屉式)                                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 基本信息                                                     │ │
│ │ 主机名: [web01        ] IP: [************] 端口: [22]      │ │
│ │ 分组:   [Web服务器 ▼  ] 标签: [生产,nginx,前端]            │ │
│ │                                                             │ │
│ │ 连接配置                                                     │ │
│ │ 用户名: [root         ] 认证: (•)密码 ( )密钥 ( )证书       │ │
│ │ 密码:   [**********  ] 密钥: [选择文件]                    │ │
│ │                                                             │ │
│ │ 高级选项                                                     │ │
│ │ 连接超时: [30秒] 重试次数: [3次] 保持连接: [☑]              │ │
│ │                                                             │ │
│ │ [测试连接] [保存] [取消]                                     │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.3 检查任务页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│  ⚡ 检查任务                                                      │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 创建新任务                                                   ││
│  │ 任务名称: [生产环境安全检查_20240120]                        ││
│  │ 目标主机: [选择主机组 ▼] [已选择: 15台主机]                  ││
│  │ 检查规则: [☑等保基线全套] [☑自定义规则A] [☐测试规则]         ││
│  │ 执行时间: (•) 立即执行 ( ) 定时执行 [2024-01-20 14:00]       ││
│  │ [创建任务] [保存模板]                                         ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  正在执行的任务                                                  │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 任务: 生产环境检查 | 进度: ████████░░ 80% | 12/15台完成      ││
│  │ 当前: 正在检查192.168.1.15 - 网络安全配置                   ││
│  │ [暂停] [停止] [查看详情]                                     ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  任务历史                                                        │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 时间        │任务名称      │主机数│状态  │通过率│操作      ││
│  ├─────────────────────────────────────────────────────────────┤│
│  │2024-01-20 10:00│生产环境检查│15台│✅完成│85%│[查看][导出]││
│  │2024-01-19 18:00│测试环境检查│8台 │✅完成│92%│[查看][导出]││
│  │2024-01-19 14:30│DB服务器检查│3台 │❌失败│--│[查看][重试]││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2.4 报告中心页面布局
```
┌─────────────────────────────────────────────────────────────────┐
│  📊 报告中心                                                     │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 筛选: 时间: [最近7天▼] 状态: [全部▼] [应用筛选]              ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  报告列表                                                        │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 生成时间      │报告名称        │主机数│通过率│操作          ││
│  ├─────────────────────────────────────────────────────────────┤│
│  │2024-01-20 10:30│生产环境安全报告│15台  │85%  │[查看][导出]  ││
│  │2024-01-19 18:15│测试环境安全报告│8台   │92%  │[查看][导出]  ││
│  │2024-01-19 14:45│数据库安全报告  │3台   │78%  │[查看][导出]  ││
│  └─────────────────────────────────────────────────────────────┘│
│                                                                 │
│  报告详情预览                                                    │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │ 📈 检查结果统计                                              ││
│  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐            ││
│  │ │ 通过项目 │ │ 警告项目 │ │ 失败项目 │ │ 总检查项 │            ││
│  │ │   127   │ │   18    │ │    8    │ │   153   │            ││
│  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘            ││
│  │                                                             ││
│  │ 🔍 主要问题分析                                              ││
│  │ • 身份鉴别类: 3台主机存在弱密码问题                          ││
│  │ • 访问控制类: 2台主机文件权限配置不当                        ││
│  │ • 安全审计类: 5台主机审计日志配置缺失                        ││
│  │                                                             ││
│  │ [导出PDF] [导出HTML]                                        ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### 4.3 前端技术实现要求

#### 4.3.1 HTML结构设计
- 语义化HTML5标签
- 模块化组件结构
- 无障碍访问支持(ARIA)
- SEO友好的标签结构

#### 4.3.2 CSS样式设计
- 采用CSS3 Flexbox/Grid布局
- 响应式设计，支持不同分辨率
- CSS变量管理主题色彩
- 动画效果增强用户体验
- 组件化CSS架构

#### 4.3.3 JavaScript功能实现
- 原生ES6+语法
- 模块化代码组织
- 事件驱动的交互逻辑
- 异步数据处理
- 本地存储管理

#### 4.3.4 UI组件库设计
```css
/* 主要色彩方案 */
:root {
  --primary-color: #2563eb;      /* 主色调-蓝色 */
  --success-color: #10b981;      /* 成功-绿色 */
  --warning-color: #f59e0b;      /* 警告-橙色 */
  --danger-color: #ef4444;       /* 危险-红色 */
  --background-color: #f8fafc;   /* 背景色 */
  --sidebar-color: #1e293b;      /* 侧边栏色 */
  --text-primary: #1f2937;       /* 主文字色 */
  --text-secondary: #6b7280;     /* 次文字色 */
  --border-color: #e5e7eb;       /* 边框色 */
}

/* 按钮组件 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-primary { background: var(--primary-color); color: white; }
.btn-success { background: var(--success-color); color: white; }
.btn-warning { background: var(--warning-color); color: white; }
.btn-danger { background: var(--danger-color); color: white; }

/* 表格组件 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 状态指示器 */
.status-online { color: var(--success-color); }
.status-offline { color: var(--danger-color); }
.status-warning { color: var(--warning-color); }
```

### 4.4 用户体验要求
- 界面简洁直观，符合现代设计规范
- 操作流程清晰，减少用户学习成本
- 支持键盘快捷键操作
- 响应式设计，适配不同屏幕尺寸
- 支持明暗主题切换
- 加载状态和进度反馈
- 友好的错误提示和帮助信息

## 5. 数据存储设计

### 5.1 数据库表结构
- 主机配置表（hosts）
- 检查规则表（rules）
- 检查任务表（tasks）
- 检查结果表（results）
- 用户配置表（settings）
- 操作日志表（logs）

### 5.2 数据安全
- 敏感数据加密存储
- 定期数据备份
- 数据完整性校验
- 支持数据导入导出

## 6. 技术实现要点

### 6.1 前端技术实现

#### 6.1.1 项目结构设计
```
frontend/
├── index.html              # 主页面入口
├── css/
│   ├── main.css            # 主样式文件
│   ├── components.css      # 组件样式
│   ├── themes.css          # 主题样式
│   └── responsive.css      # 响应式样式
├── js/
│   ├── main.js             # 主应用逻辑
│   ├── api.js              # API通信模块
│   ├── components/         # UI组件
│   │   ├── table.js        # 表格组件
│   │   ├── form.js         # 表单组件
│   │   ├── chart.js        # 图表组件
│   │   └── modal.js        # 模态框组件
│   ├── pages/              # 页面模块
│   │   ├── dashboard.js    # 仪表盘页面
│   │   ├── hosts.js        # 主机管理页面
│   │   ├── rules.js        # 检查规则页面
│   │   ├── tasks.js        # 检查任务页面
│   │   └── reports.js      # 报告中心页面
│   └── utils/              # 工具函数
│       ├── storage.js      # 本地存储
│       ├── validator.js    # 数据验证
│       └── formatter.js    # 数据格式化
├── assets/
│   ├── icons/              # 图标文件
│   ├── images/             # 图片资源
│   └── fonts/              # 字体文件
└── lib/
    ├── chart.min.js        # Chart.js图表库
    └── utils.min.js        # 工具库
```

#### 6.1.2 核心JavaScript模块
```javascript
// main.js - 主应用入口
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupNavigation();
        this.loadPage(this.currentPage);
        this.setupEventListeners();
    }

    setupNavigation() {
        // 导航菜单事件绑定
    }

    loadPage(pageName) {
        // 动态加载页面内容
    }
}

// api.js - 与Go后端通信
class API {
    static async call(method, params) {
        // 通过Wails调用Go后端方法
        return window.go.main.App[method](params);
    }

    static async getHosts() {
        return this.call('GetHosts');
    }

    static async createTask(taskData) {
        return this.call('CreateTask', taskData);
    }
}
```

### 6.2 SSH连接管理
- 连接池管理，复用连接
- 超时和重试机制
- 并发连接数控制
- 连接状态监控

### 6.3 命令执行引擎
- 命令模板化管理
- 结果解析和匹配
- 异常处理机制
- 执行日志记录

### 6.4 规则引擎设计
- 可配置的检查规则
- 支持条件判断和逻辑运算
- 结果评分算法
- 规则依赖关系处理

## 7. 项目交付物

### 6.5 报告导出功能设计

#### 6.5.1 PDF导出
- 使用Go PDF库生成标准PDF格式
- 支持中文字体和图表嵌入
- 包含完整的检查结果和统计信息
- 适合正式文档归档和打印

#### 6.5.2 HTML导出
- 生成独立的HTML文件，包含CSS样式
- 支持交互式图表和数据展示
- 响应式设计，适配不同设备查看
- 包含JavaScript交互功能（如数据筛选、排序）
- 可在任何现代浏览器中打开查看
- 支持自定义主题和样式模板

#### 6.5.3 导出功能特性
- 批量导出多个报告
- 自定义导出内容和格式
- 导出进度显示和取消功能
- 导出文件自动命名和路径管理

### 7.1 软件交付
- 跨平台可执行程序
- 安装包和部署文档
- 用户操作手册
- 管理员配置指南

### 7.2 文档交付
- 详细设计文档
- API接口文档
- 测试报告
- 运维手册

## 8. 项目计划

### 8.1 开发阶段
- 第一阶段：基础框架搭建（2周）
- 第二阶段：核心功能开发（4周）
- 第三阶段：界面开发和集成（3周）
- 第四阶段：测试和优化（2周）

### 8.2 里程碑
- M1：完成项目架构设计和技术选型
- M2：完成SSH连接和基础检查功能
- M3：完成GUI界面和用户交互
- M4：完成系统测试和文档编写

## 9. 风险评估

### 9.1 技术风险
- SSH连接稳定性问题
- 不同Linux发行版兼容性
- 大规模并发检查性能瓶颈

### 9.2 缓解措施
- 充分的兼容性测试
- 性能压力测试
- 异常处理机制完善
- 分阶段交付降低风险
